import { Routes, Route } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Toaster } from 'react-hot-toast';
import { Layout } from './components/layout/Layout';
import { Footer } from './components/layout/Footer';
import { ProtectedRoute } from './components/layout/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { LoadingSpinner } from './components/ui/utils';
import { AppWrapper } from './components/layout/AppWrapper';

// Lazy load components for better performance
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Search = lazy(() => import('./pages/Search'));
const Library = lazy(() => import('./pages/Library'));
const Wishlist = lazy(() => import('./pages/Wishlist'));
const Deals = lazy(() => import('./pages/Deals'));
const Settings = lazy(() => import('./pages/Settings'));
const Profile = lazy(() => import('./pages/Profile'));
const AIAgent = lazy(() => import('./pages/AIAgent'));
const GameDetail = lazy(() => import('./pages/GameDetail'));

// Keep auth pages non-lazy for faster initial load
import Login from './pages/Login';
import SignUp from './pages/SignUp';

// Demo components for development
import { SteamLibraryDemo } from './components/examples/SteamLibraryDemo';
import { ProductionLibraryDemo } from './components/examples/ProductionLibraryDemo';

export default function App() {
  return (
    <AuthProvider>
      <AppWrapper>
        <Routes>
        {/* Public routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<SignUp />} />
        <Route path="/demo/steam-library" element={<SteamLibraryDemo />} />
        <Route path="/demo/production-library" element={<ProductionLibraryDemo />} />
        
        {/* Protected routes */}
        <Route path="/*" element={
          <ProtectedRoute>
            <Layout>
              <Suspense fallback={
                <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4 animate-fade-in">
                  <LoadingSpinner size="lg" variant="pulse" />
                  <p className="text-muted-foreground text-sm animate-pulse">Loading...</p>
                </div>
              }>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/search" element={<Search />} />
                  <Route path="/library" element={<Library />} />
                  <Route path="/wishlist" element={<Wishlist />} />
                  <Route path="/deals" element={<Deals />} />
                  <Route path="/ai-agent" element={<AIAgent />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/game/:id" element={<GameDetail />} />
                </Routes>
              </Suspense>
              <Footer />
            </Layout>
          </ProtectedRoute>
        } />
      </Routes>
      </AppWrapper>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'hsl(var(--background) / 0.95)',
            color: 'hsl(var(--foreground))',
            border: '1px solid hsl(var(--border) / 0.5)',
            borderRadius: '0.75rem',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
            fontWeight: '500',
          },
          success: {
            iconTheme: {
              primary: 'hsl(var(--primary))',
              secondary: 'hsl(var(--background))',
            },
          },
          error: {
            iconTheme: {
              primary: 'hsl(var(--destructive))',
              secondary: 'hsl(var(--background))',
            },
          },
        }}
      />
    </AuthProvider>
  );
}