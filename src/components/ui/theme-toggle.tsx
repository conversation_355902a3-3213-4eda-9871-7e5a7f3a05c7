import { useState, useEffect } from 'react';
import { Monitor, Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/base/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/base/dropdown-menu';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/base/tooltip';
import { useTheme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export function ThemeToggle({ 
  variant = 'dropdown', 
  size = 'md', 
  showLabel = false,
  className 
}: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme, systemTheme, isLoading } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || isLoading) {
    return (
      <Button
        variant="ghost"
        size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
        className={cn(
          "transition-all duration-200",
          size === 'sm' && "h-8 w-8",
          size === 'md' && "h-9 w-9",
          size === 'lg' && "h-10 w-10",
          className
        )}
        disabled
        aria-label="Loading theme toggle"
      >
        <div className="h-4 w-4 animate-pulse bg-muted rounded" />
      </Button>
    );
  }

  const getThemeIcon = (themeType: string) => {
    switch (themeType) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getThemeLabel = (themeType: string) => {
    switch (themeType) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return `System (${systemTheme})`;
      default:
        return 'System';
    }
  };

  const getNextTheme = () => {
    switch (theme) {
      case 'light':
        return 'dark';
      case 'dark':
        return 'system';
      case 'system':
        return 'light';
      default:
        return 'light';
    }
  };

  // Simple button toggle (cycles through themes)
  if (variant === 'button') {
    const handleToggle = () => {
      const nextTheme = getNextTheme();
      setTheme(nextTheme);
      
      // Announce theme change for screen readers
      const announcement = `Theme changed to ${getThemeLabel(nextTheme)}`;
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('aria-atomic', 'true');
      announcer.className = 'sr-only';
      announcer.textContent = announcement;
      document.body.appendChild(announcer);
      setTimeout(() => document.body.removeChild(announcer), 1000);
    };

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
            className={cn(
              "transition-all duration-200 hover:bg-accent/50 hover:scale-105 focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
              size === 'sm' && "h-8 w-8",
              size === 'md' && "h-9 w-9", 
              size === 'lg' && "h-10 w-10",
              showLabel && "gap-2 px-3",
              className
            )}
            onClick={handleToggle}
            aria-label={`Switch to ${getThemeLabel(getNextTheme())}`}
          >
            <span className="transition-transform duration-200 hover:rotate-12">
              {getThemeIcon(theme)}
            </span>
            {showLabel && (
              <span className="text-sm font-medium">
                {getThemeLabel(theme)}
              </span>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="font-medium">
          <p>Current: {getThemeLabel(theme)}</p>
          <p className="text-xs text-muted-foreground">
            Click to switch to {getThemeLabel(getNextTheme())}
          </p>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Dropdown menu with all theme options
  return (
    <DropdownMenu>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
              className={cn(
                "transition-all duration-200 hover:bg-accent/50 hover:scale-105 focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
                size === 'sm' && "h-8 w-8",
                size === 'md' && "h-9 w-9",
                size === 'lg' && "h-10 w-10",
                showLabel && "gap-2 px-3",
                className
              )}
              aria-label="Open theme selector"
            >
              <span className="transition-transform duration-200 hover:rotate-12">
                {getThemeIcon(theme)}
              </span>
              {showLabel && (
                <span className="text-sm font-medium">
                  {getThemeLabel(theme)}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="font-medium">
          <p>Theme: {getThemeLabel(theme)}</p>
          <p className="text-xs text-muted-foreground">
            {resolvedTheme === 'dark' ? 'Dark mode active' : 'Light mode active'}
          </p>
        </TooltipContent>
      </Tooltip>
      
      <DropdownMenuContent 
        align="end" 
        className="w-48 animate-in fade-in-0 zoom-in-95 slide-in-from-top-2"
      >
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={cn(
            "cursor-pointer gap-3 py-2.5 focus:bg-accent/50",
            theme === 'light' && "bg-accent/30 text-accent-foreground"
          )}
          aria-current={theme === 'light' ? 'true' : 'false'}
        >
          <Sun className="h-4 w-4" />
          <div className="flex flex-col gap-0.5">
            <span className="font-medium">Light</span>
            <span className="text-xs text-muted-foreground">
              Bright and clean interface
            </span>
          </div>
          {theme === 'light' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={cn(
            "cursor-pointer gap-3 py-2.5 focus:bg-accent/50",
            theme === 'dark' && "bg-accent/30 text-accent-foreground"
          )}
          aria-current={theme === 'dark' ? 'true' : 'false'}
        >
          <Moon className="h-4 w-4" />
          <div className="flex flex-col gap-0.5">
            <span className="font-medium">Dark</span>
            <span className="text-xs text-muted-foreground">
              Easy on the eyes
            </span>
          </div>
          {theme === 'dark' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={cn(
            "cursor-pointer gap-3 py-2.5 focus:bg-accent/50",
            theme === 'system' && "bg-accent/30 text-accent-foreground"
          )}
          aria-current={theme === 'system' ? 'true' : 'false'}
        >
          <Monitor className="h-4 w-4" />
          <div className="flex flex-col gap-0.5">
            <span className="font-medium">System</span>
            <span className="text-xs text-muted-foreground">
              Follows device preference ({systemTheme})
            </span>
          </div>
          {theme === 'system' && (
            <div className="ml-auto h-2 w-2 rounded-full bg-primary" />
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact theme toggle for mobile or space-constrained areas
export function CompactThemeToggle({ className }: { className?: string }) {
  return (
    <ThemeToggle 
      variant="button" 
      size="sm" 
      className={className}
    />
  );
}

// Theme toggle with label for settings pages
export function LabeledThemeToggle({ className }: { className?: string }) {
  return (
    <ThemeToggle 
      variant="dropdown" 
      size="md" 
      showLabel 
      className={className}
    />
  );
}
