import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';

// Theme types
export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

// Theme preference storage interface
interface ThemePreference {
  theme: Theme;
  lastUpdated: string;
  autoDetectSystem: boolean;
}

// Theme context interface
interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  systemTheme: ResolvedTheme;
  isLoading: boolean;
  error: string | null;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Custom hook to use theme context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

// System preference detection with error handling
function getSystemTheme(): ResolvedTheme {
  try {
    if (typeof window === 'undefined') return 'light';
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    return mediaQuery.matches ? 'dark' : 'light';
  } catch (error) {
    console.warn('Failed to detect system theme preference:', error);
    return 'light';
  }
}

// Enhanced theme storage helpers with validation
function getStoredTheme(storageKey: string): ThemePreference | null {
  try {
    const stored = localStorage.getItem(storageKey);
    if (!stored) return null;
    
    const parsed = JSON.parse(stored) as ThemePreference;
    
    // Validate the stored data
    if (
      parsed &&
      typeof parsed === 'object' &&
      ['light', 'dark', 'system'].includes(parsed.theme) &&
      typeof parsed.lastUpdated === 'string' &&
      typeof parsed.autoDetectSystem === 'boolean'
    ) {
      return parsed;
    }
    
    // Invalid data, remove it
    localStorage.removeItem(storageKey);
    return null;
  } catch (error) {
    console.warn('Failed to read theme from localStorage:', error);
    // Clear corrupted data
    try {
      localStorage.removeItem(storageKey);
    } catch (clearError) {
      console.warn('Failed to clear corrupted theme data:', clearError);
    }
    return null;
  }
}

function setStoredTheme(storageKey: string, theme: Theme): void {
  try {
    const preference: ThemePreference = {
      theme,
      lastUpdated: new Date().toISOString(),
      autoDetectSystem: theme === 'system'
    };
    
    localStorage.setItem(storageKey, JSON.stringify(preference));
  } catch (error) {
    console.warn('Failed to save theme to localStorage:', error);
  }
}

// Apply theme to document with smooth transitions
function applyTheme(theme: ResolvedTheme): void {
  try {
    const root = document.documentElement;

    // Add transition class for smooth theme changes
    root.classList.add('theme-changing');

    // Remove existing theme classes
    root.classList.remove('light', 'dark');

    // Add new theme class
    root.classList.add(theme);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      // Get the background color from CSS variables
      const bgColor = theme === 'dark'
        ? 'hsl(222, 84%, 5%)' // Dark background
        : 'hsl(250, 20%, 98%)'; // Light background
      metaThemeColor.setAttribute('content', bgColor);
    }

    // Update viewport meta for mobile browsers
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (viewportMeta) {
      const content = viewportMeta.getAttribute('content') || '';
      const themeColorScheme = theme === 'dark' ? 'dark' : 'light';

      // Add or update color-scheme in viewport meta
      if (!content.includes('color-scheme')) {
        viewportMeta.setAttribute('content', `${content}, color-scheme=${themeColorScheme}`);
      } else {
        const updatedContent = content.replace(
          /color-scheme=(light|dark)/,
          `color-scheme=${themeColorScheme}`
        );
        viewportMeta.setAttribute('content', updatedContent);
      }
    }

    // Set color-scheme CSS property for better browser integration
    root.style.colorScheme = theme;

    // Remove transition class after a short delay to re-enable smooth transitions
    setTimeout(() => {
      root.classList.remove('theme-changing');
      root.classList.add('theme-transition-complete');

      // Remove the completion class after transition duration
      setTimeout(() => {
        root.classList.remove('theme-transition-complete');
      }, 300);
    }, 50);
    
    // Remove transition class after a brief delay to allow CSS to apply
    setTimeout(() => {
      root.classList.remove('theme-changing');
      root.classList.add('theme-changed');
      
      // Remove the changed class after transition completes
      setTimeout(() => {
        root.classList.remove('theme-changed');
      }, 300);
    }, 10);
    
  } catch (error) {
    console.warn('Failed to apply theme:', error);
  }
}

// Announce theme changes for screen readers
function announceThemeChange(theme: ResolvedTheme): void {
  try {
    const announcement = `Theme changed to ${theme} mode`;
    
    // Create live region for screen readers
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only absolute -left-[10000px] w-[1px] h-[1px] overflow-hidden';
    liveRegion.textContent = announcement;
    
    document.body.appendChild(liveRegion);
    
    // Remove after announcement
    setTimeout(() => {
      if (document.body.contains(liveRegion)) {
        document.body.removeChild(liveRegion);
      }
    }, 1000);
  } catch (error) {
    console.warn('Failed to announce theme change:', error);
  }
}

// Error handler for theme system
function handleThemeError(error: Error, setError: (error: string | null) => void): void {
  console.warn('Theme system error:', error);
  setError(error.message);
  
  // Clear error after 5 seconds
  setTimeout(() => {
    setError(null);
  }, 5000);
}

// Theme provider component
export function ThemeProvider({ 
  children, 
  defaultTheme = 'system',
  storageKey = 'codexa-theme-preference'
}: ThemeProviderProps) {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>(() => getSystemTheme());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Calculate resolved theme
  const resolvedTheme: ResolvedTheme = theme === 'system' ? systemTheme : theme;

  // Initialize theme from storage or default
  useEffect(() => {
    try {
      const storedPreference = getStoredTheme(storageKey);
      
      if (storedPreference) {
        setThemeState(storedPreference.theme);
      } else {
        // If no stored theme, detect system preference
        const detectedTheme = getSystemTheme();
        setSystemTheme(detectedTheme);
        setThemeState(defaultTheme);
      }
    } catch (error) {
      handleThemeError(error as Error, setError);
      setThemeState('light'); // Fallback to light theme
    } finally {
      setIsLoading(false);
    }
  }, [defaultTheme, storageKey]);

  // Listen for system theme changes
  useEffect(() => {
    try {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        try {
          const newSystemTheme = e.matches ? 'dark' : 'light';
          setSystemTheme(newSystemTheme);
        } catch (error) {
          handleThemeError(error as Error, setError);
        }
      };

      // Add listener
      mediaQuery.addEventListener('change', handleChange);
      
      // Cleanup
      return () => {
        try {
          mediaQuery.removeEventListener('change', handleChange);
        } catch (error) {
          console.warn('Failed to remove media query listener:', error);
        }
      };
    } catch (error) {
      handleThemeError(error as Error, setError);
    }
  }, []);

  // Apply theme when resolved theme changes
  useEffect(() => {
    if (!isLoading) {
      applyTheme(resolvedTheme);
      announceThemeChange(resolvedTheme);
    }
  }, [resolvedTheme, isLoading]);

  // Theme setter with persistence and error handling
  const setTheme = useCallback((newTheme: Theme) => {
    try {
      setThemeState(newTheme);
      setStoredTheme(storageKey, newTheme);
      setError(null); // Clear any previous errors
    } catch (error) {
      handleThemeError(error as Error, setError);
    }
  }, [storageKey]);

  const value: ThemeContextType = {
    theme,
    resolvedTheme,
    setTheme,
    systemTheme,
    isLoading,
    error,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}