# Dark Mode Implementation Guide

This document outlines the comprehensive dark mode implementation for the Codexa gaming web application, featuring full accessibility support, smooth transitions, and persistent user preferences.

## 🌟 Features

### Core Functionality
- **Three Theme Options**: Light, Dark, and System (follows OS preference)
- **Persistent Storage**: User preferences saved to localStorage
- **System Integration**: Respects `prefers-color-scheme` media query
- **Smooth Transitions**: Animated theme switching with CSS transitions
- **Real-time Updates**: Automatic theme switching when system preference changes

### Accessibility Features
- **WCAG Compliance**: AA and AAA color contrast ratios
- **Keyboard Navigation**: Full keyboard accessibility for theme controls
- **Screen Reader Support**: Proper ARIA labels and announcements
- **High Contrast Mode**: Enhanced contrast for users with visual impairments
- **Reduced Motion**: Respects `prefers-reduced-motion` preference
- **Focus Management**: Clear focus indicators and logical tab order

## 🏗️ Architecture

### File Structure
```
src/
├── components/ui/
│   └── theme-toggle.tsx          # Main theme toggle component
├── contexts/
│   └── ThemeContext.tsx          # Theme context provider
├── hooks/
│   ├── useTheme.ts              # Theme context hook
│   └── useThemePreference.ts    # Advanced theme preference hook
├── styles/
│   ├── index.css               # Main styles with CSS variables
│   └── accessibility.css      # Accessibility enhancements
└── components/examples/
    └── ThemeDemo.tsx           # Demo page showcasing features
```

### CSS Variables System
The theme system uses CSS custom properties for consistent theming:

```css
:root {
  /* Light theme variables */
  --background: 250 20% 98%;
  --foreground: 222 84% 5%;
  --primary: 262 83% 58%;
  /* ... more variables */
}

.dark {
  /* Dark theme variables */
  --background: 222 84% 5%;
  --foreground: 210 40% 98%;
  --primary: 262 83% 58%;
  /* ... more variables */
}
```

## 🚀 Usage

### Basic Theme Toggle
```tsx
import { ThemeToggle } from '@/components/ui/theme-toggle';

// Dropdown with all options
<ThemeToggle variant="dropdown" size="md" />

// Simple button toggle
<ThemeToggle variant="button" size="sm" />

// With label for settings
<ThemeToggle variant="dropdown" size="md" showLabel />
```

### Using Theme Context
```tsx
import { useTheme } from '@/hooks/useTheme';

function MyComponent() {
  const { theme, resolvedTheme, setTheme, systemTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <button onClick={() => setTheme('dark')}>
        Switch to Dark
      </button>
    </div>
  );
}
```

### Advanced Theme Preferences
```tsx
import { useThemePreference } from '@/hooks/useThemePreference';

function AdvancedSettings() {
  const { 
    theme, 
    resolvedTheme, 
    setTheme, 
    preferences 
  } = useThemePreference();
  
  return (
    <div>
      <p>Contrast: {preferences.contrast}</p>
      <p>Motion: {preferences.motion}</p>
      <p>Color Scheme: {preferences.colorScheme}</p>
    </div>
  );
}
```

## 🎨 Customization

### Adding New Color Variables
1. Add the variable to both light and dark themes in `src/styles/index.css`:
```css
:root {
  --my-custom-color: 200 50% 60%;
}

.dark {
  --my-custom-color: 200 50% 40%;
}
```

2. Add to Tailwind config if needed:
```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'my-custom': 'hsl(var(--my-custom-color))',
      }
    }
  }
}
```

### Custom Theme Toggle
```tsx
import { useTheme } from '@/hooks/useTheme';
import { Moon, Sun, Monitor } from 'lucide-react';

function CustomThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  const icons = {
    light: Sun,
    dark: Moon,
    system: Monitor,
  };
  
  const Icon = icons[theme];
  
  return (
    <button 
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      <Icon className="h-4 w-4" />
    </button>
  );
}
```

## ♿ Accessibility Guidelines

### Color Contrast
- **AA Standard**: Minimum 4.5:1 ratio for normal text
- **AAA Standard**: Minimum 7:1 ratio for enhanced accessibility
- **Large Text**: Minimum 3:1 ratio for text 18pt+ or 14pt+ bold

### Keyboard Navigation
- All theme controls are keyboard accessible
- Focus indicators are clearly visible
- Logical tab order maintained

### Screen Reader Support
- Theme changes are announced to screen readers
- Proper ARIA labels on all interactive elements
- Current theme state is communicated

### Motion Preferences
```css
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
```

## 🔧 Configuration

### Theme Provider Setup
```tsx
// src/main.tsx or App.tsx
import { ThemeProvider } from '@/contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="my-app-theme">
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

### Storage Configuration
The theme preference is stored in localStorage with the following structure:
```json
{
  "theme": "dark",
  "lastUpdated": "2024-01-15T10:30:00.000Z",
  "autoDetectSystem": true,
  "contrastPreference": "normal",
  "motionPreference": "normal",
  "version": "1.0"
}
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Theme persists across page reloads
- [ ] System theme changes are detected
- [ ] Smooth transitions work (when motion not reduced)
- [ ] High contrast mode is supported
- [ ] Keyboard navigation works
- [ ] Screen reader announcements work
- [ ] All color contrasts meet WCAG standards

### Browser Testing
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers

### Accessibility Testing
- [ ] Test with screen readers (NVDA, JAWS, VoiceOver)
- [ ] Test keyboard-only navigation
- [ ] Test with high contrast mode enabled
- [ ] Test with reduced motion enabled
- [ ] Validate color contrast ratios

## 🐛 Troubleshooting

### Common Issues

**Theme not persisting**
- Check localStorage permissions
- Verify storage key is consistent
- Check for localStorage quota limits

**Transitions not working**
- Verify CSS variables are properly defined
- Check for `prefers-reduced-motion` override
- Ensure transition classes are applied

**System theme not detected**
- Check browser support for `prefers-color-scheme`
- Verify media query listeners are attached
- Check for JavaScript errors in console

**Accessibility issues**
- Validate ARIA labels are present
- Check focus indicators are visible
- Verify screen reader announcements

## 📚 Resources

- [WCAG Color Contrast Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
- [CSS Color Scheme Property](https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme)
- [Prefers Color Scheme](https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-color-scheme)
- [Accessible Theme Switching](https://web.dev/prefers-color-scheme/)

## 🤝 Contributing

When contributing to the theme system:
1. Ensure all new colors meet WCAG AA standards
2. Test with screen readers and keyboard navigation
3. Verify theme transitions work smoothly
4. Update documentation for new features
5. Add appropriate TypeScript types
